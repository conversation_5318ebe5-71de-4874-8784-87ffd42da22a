import { join } from 'path';

import { BrowserWindow } from 'electron';
import { z } from 'zod';

export interface AppConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  homepage: string;
  repository: string;
  license: string;
  isDev: boolean;
  isProd: boolean;
  isTest: boolean;
  platform: NodeJS.Platform;
  paths: {
    root: string;
    src: string;
    dist: string;
    assets: string;
    locales: string;
    extensions: string;
    userData: string;
    logs: string;
    temp: string;
  };
  window: {
    defaultWidth: number;
    defaultHeight: number;
    minWidth: number;
    minHeight: number;
    title: string;
    backgroundColor: string;
    showDevTools: boolean;
  };
  extensions: {
    enabled: boolean;
    autoUpdate: boolean;
    sandbox: boolean;
    maxMemory: number;
    maxCpu: number;
  };
  security: {
    sandbox: boolean;
    contextIsolation: boolean;
    nodeIntegration: boolean;
    webSecurity: boolean;
    allowRunningInsecureContent: boolean;
    enableRemoteModule: boolean;
  };
  performance: {
    maxMemory: number;
    maxCpu: number;
    maxTabs: number;
    tabSleepTimeout: number;
    cacheSize: number;
  };
  privacy: {
    doNotTrack: boolean;
    blockAds: boolean;
    blockTrackers: boolean;
    blockFingerprinting: boolean;
    blockWebRTC: boolean;
    clearOnExit: boolean;
  };
  sync: {
    enabled: boolean;
    interval: number;
    maxRetries: number;
    timeout: number;
  };
  updates: {
    autoUpdate: boolean;
    checkInterval: number;
    channel: 'stable' | 'beta' | 'nightly';
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    maxSize: number;
    maxFiles: number;
  };
}

const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';

const config: AppConfig = {
  name: 'NovaBrowser',
  version: process.env.npm_package_version || '1.0.0',
  description: process.env.npm_package_description || '',
  author: process.env.npm_package_author || '',
  homepage: process.env.npm_package_homepage || '',
  repository: process.env.npm_package_repository || '',
  license: process.env.npm_package_license || 'MIT',
  isDev,
  isProd,
  isTest,
  platform: process.platform,
  paths: {
    root: process.cwd(),
    src: join(process.cwd(), 'src'),
    dist: join(process.cwd(), 'dist'),
    assets: join(process.cwd(), 'src/assets'),
    locales: join(process.cwd(), 'src/i18n/locales'),
    extensions: join(process.cwd(), 'extensions'),
    userData: join(process.cwd(), 'userData'),
    logs: join(process.cwd(), 'logs'),
    temp: join(process.cwd(), 'temp'),
  },
  window: {
    defaultWidth: 1280,
    defaultHeight: 800,
    minWidth: 800,
    minHeight: 600,
    title: 'NovaBrowser',
    backgroundColor: '#ffffff',
    showDevTools: isDev,
  },
  extensions: {
    enabled: true,
    autoUpdate: true,
    sandbox: true,
    maxMemory: 512, // MB
    maxCpu: 50, // percentage
  },
  security: {
    sandbox: true,
    contextIsolation: true,
    nodeIntegration: false,
    webSecurity: true,
    allowRunningInsecureContent: false,
    enableRemoteModule: false,
  },
  performance: {
    maxMemory: 2048, // MB
    maxCpu: 80, // percentage
    maxTabs: 100,
    tabSleepTimeout: 300000, // 5 minutes
    cacheSize: 1024, // MB
  },
  privacy: {
    doNotTrack: true,
    blockAds: true,
    blockTrackers: true,
    blockFingerprinting: true,
    blockWebRTC: true,
    clearOnExit: false,
  },
  sync: {
    enabled: true,
    interval: 300000, // 5 minutes
    maxRetries: 3,
    timeout: 30000, // 30 seconds
  },
  updates: {
    autoUpdate: true,
    checkInterval: 3600000, // 1 hour
    channel: 'stable',
  },
  logging: {
    level: isDev ? 'debug' : 'info',
    maxSize: 10, // MB
    maxFiles: 5,
  },
};

export default config;

export const AppConfigSchema = z.object({
  app: z.object({
    name: z.string(),
    version: z.string(),
    environment: z.enum(['development', 'staging', 'production']),
    debug: z.boolean(),
    logLevel: z.enum(['error', 'warn', 'info', 'debug', 'trace']),
  }),
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number(),
    retryAttempts: z.number(),
    retryDelay: z.number(),
  }),
  security: z.object({
    encryption: z.object({
      algorithm: z.string(),
      keySize: z.number(),
      iterations: z.number(),
    }),
    jwt: z.object({
      secret: z.string(),
      expiresIn: z.string(),
      refreshExpiresIn: z.string(),
    }),
  }),
  performance: z.object({
    cache: z.object({
      enabled: z.boolean(),
      ttl: z.number(),
      maxSize: z.number(),
    }),
    compression: z.object({
      enabled: z.boolean(),
      level: z.number(),
    }),
  }),
  monitoring: z.object({
    enabled: z.boolean(),
    metrics: z.object({
      enabled: z.boolean(),
      interval: z.number(),
    }),
    tracing: z.object({
      enabled: z.boolean(),
      sampling: z.number(),
    }),
  }),
  analytics: z.object({
    enabled: z.boolean(),
    providers: z.array(z.string()),
    events: z.array(z.string()),
  }),
  accessibility: z.object({
    enabled: z.boolean(),
    features: z.array(z.string()),
    compliance: z.array(z.string()),
  }),
});

export type AppConfig = z.infer<typeof AppConfigSchema>;

export const defaultConfig: AppConfig = {
  app: {
    name: 'A14-Browser',
    version: '1.0.0',
    environment: 'development',
    debug: true,
    logLevel: 'info',
  },
  api: {
    baseUrl: 'http://localhost:3000',
    timeout: 5000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keySize: 32,
      iterations: 100000,
    },
    jwt: {
      secret: process.env.JWT_SECRET || 'your-secret-key',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    },
  },
  performance: {
    cache: {
      enabled: true,
      ttl: 3600,
      maxSize: 1000,
    },
    compression: {
      enabled: true,
      level: 6,
    },
  },
  monitoring: {
    enabled: true,
    metrics: {
      enabled: true,
      interval: 60000,
    },
    tracing: {
      enabled: true,
      sampling: 0.1,
    },
  },
  analytics: {
    enabled: true,
    providers: ['google-analytics', 'mixpanel'],
    events: ['page_view', 'user_action', 'error'],
  },
  accessibility: {
    enabled: true,
    features: ['screen-reader', 'keyboard-navigation', 'high-contrast'],
    compliance: ['WCAG2.1', 'ADA'],
  },
};
