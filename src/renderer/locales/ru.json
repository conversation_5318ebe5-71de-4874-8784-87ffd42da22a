{"app.name": "A11 Браузер", "app.description": "Современный и безопасный веб-браузер", "tabs.new": "Новая вкладка", "tabs.close": "Закрыть вкладку", "tabs.closeOthers": "Закрыть другие вкладки", "tabs.reopen": "Восстановить закрытую вкладку", "tabs.group": "Группировать вкладки", "tabs.ungroup": "Разгруппировать вкладки", "tabs.groupName": "Название группы", "navigation.back": "Назад", "navigation.forward": "Вперёд", "navigation.reload": "Обновить", "navigation.home": "Домашняя страница", "bookmarks.add": "Добавить в закладки", "bookmarks.remove": "Удалить из закладок", "bookmarks.all": "Все закладки", "history.show": "История", "history.clear": "Очистить историю", "downloads.show": "Загрузки", "settings.show": "Настройки", "settings.theme": "Тема оформления", "settings.language": "Язык интерфейса", "settings.privacy": "Конфиденциальность", "settings.security": "Безопасность", "settings.advanced": "Дополнительно", "search.placeholder": "Поиск или адрес сайта", "search.engines": "Поисковые системы", "error.pageNotFound": "Страница не найдена", "error.connectionFailed": "Ошибка соединения", "error.insecureConnection": "Небезопасное соединение", "security.phishingDetected": "Обнаружен фишинговый сайт", "security.malwareDetected": "Обнаружен вредоносный сайт", "security.certificateError": "Ошибка сертификата", "security.proceedAnyway": "Всё равно продолжить (небезопасно)", "security.goBack": "Вернуться назад", "preload.status": "Предзагрузка страниц", "preload.enable": "Включить предзагрузку", "preload.disable": "Отключить предзагрузку", "wasm.status": "Поддержка WebAssembly", "wasm.enable": "Включить WebAssembly", "wasm.disable": "Отключить WebAssembly", "browser.title": "A14 Браузер", "browser.welcome": "Добро пожаловать в A14 Браузер! Это современный, функциональный браузер на React и Electron.", "errorBoundary.header": "Что-то пошло не так", "errorBoundary.reload": "Перезагрузить страницу"}