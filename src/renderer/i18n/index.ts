import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

// Import translations
import deTranslation from './locales/de.json';
import enTranslation from './locales/en.json';
import esTranslation from './locales/es.json';
import frTranslation from './locales/fr.json';
import jaTranslation from './locales/ja.json';
import koTranslation from './locales/ko.json';
import ruTranslation from './locales/ru.json';
import zhTranslation from './locales/zh.json';

const resources = {
  en: {
    translation: enTranslation,
  },
  ru: {
    translation: ruTranslation,
  },
  zh: {
    translation: zhTranslation,
  },
  es: {
    translation: esTranslation,
  },
  fr: {
    translation: frTranslation,
  },
  de: {
    translation: deTranslation,
  },
  ja: {
    translation: jaTranslation,
  },
  ko: {
    translation: koTranslation,
  },
};

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

export default i18n;

export const languages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
];

export const getCurrentLanguage = () => i18n.language;

export const setLanguage = (language: string) => {
  i18n.changeLanguage(language);
  document.documentElement.lang = language;
  document.documentElement.dir = getTextDirection(language);
};

export const getTextDirection = (language: string) => {
  const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
  return rtlLanguages.includes(language) ? 'rtl' : 'ltr';
};

export const formatDate = (date: Date, options: Intl.DateTimeFormatOptions = {}) => {
  const language = getCurrentLanguage();
  return new Intl.DateTimeFormat(language, options).format(date);
};

export const formatNumber = (number: number, options: Intl.NumberFormatOptions = {}) => {
  const language = getCurrentLanguage();
  return new Intl.NumberFormat(language, options).format(number);
};

export const formatCurrency = (amount: number, currency: string = 'USD') => {
  const language = getCurrentLanguage();
  return new Intl.NumberFormat(language, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatRelativeTime = (date: Date) => {
  const language = getCurrentLanguage();
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const rtf = new Intl.RelativeTimeFormat(language, { numeric: 'auto' });

  if (days > 0) return rtf.format(-days, 'day');
  if (hours > 0) return rtf.format(-hours, 'hour');
  if (minutes > 0) return rtf.format(-minutes, 'minute');
  return rtf.format(-seconds, 'second');
};

export const formatFileSize = (bytes: number) => {
  const language = getCurrentLanguage();
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${new Intl.NumberFormat(language, {
    style: 'decimal',
    maximumFractionDigits: 2,
  }).format(size)} ${units[unitIndex]}`;
};

export const formatDuration = (seconds: number) => {
  const language = getCurrentLanguage();
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts = [];
  if (hours > 0) parts.push(hours.toString().padStart(2, '0'));
  parts.push(minutes.toString().padStart(2, '0'));
  parts.push(remainingSeconds.toString().padStart(2, '0'));

  return parts.join(':');
};

export const formatPhoneNumber = (phoneNumber: string) => {
  const language = getCurrentLanguage();
  try {
    return new Intl.PhoneNumberFormat(language).format(phoneNumber);
  } catch {
    return phoneNumber;
  }
};

export const formatList = (items: string[]) => {
  const language = getCurrentLanguage();
  return new Intl.ListFormat(language, {
    style: 'long',
    type: 'conjunction',
  }).format(items);
};

export const formatPlural = (count: number, singular: string, plural: string) => {
  const language = getCurrentLanguage();
  const formatter = new Intl.PluralRules(language);
  const form = formatter.select(count);
  return count === 1 ? singular : plural;
};
