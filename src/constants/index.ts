export const APP_NAME = 'A14-Browser';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Advanced web browser with enhanced security and performance';

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
  },
  USER: {
    PROFILE: '/user/profile',
    SETTINGS: '/user/settings',
    PREFERENCES: '/user/preferences',
  },
  SECURITY: {
    ENCRYPTION: '/security/encryption',
    DECRYPTION: '/security/decryption',
    TOKEN: '/security/token',
  },
  ANALYTICS: {
    EVENTS: '/analytics/events',
    METRICS: '/analytics/metrics',
  },
};

export const SECURITY = {
  ENCRYPTION: {
    ALGORITHM: 'aes-256-gcm',
    KEY_SIZE: 32,
    ITERATIONS: 100000,
  },
  JWT: {
    EXPIRES_IN: '1h',
    REFRESH_EXPIRES_IN: '7d',
  },
};

export const PERFORMANCE = {
  CACHE: {
    TTL: 3600,
    MAX_SIZE: 1000,
  },
  COMPRESSION: {
    LEVEL: 6,
  },
};

export const MONITORING = {
  METRICS: {
    INTERVAL: 60000,
  },
  TRACING: {
    SAMPLING: 0.1,
  },
};

export const ANALYTICS = {
  PROVIDERS: ['google-analytics', 'mixpanel'],
  EVENTS: ['page_view', 'user_action', 'error'],
};

export const ACCESSIBILITY = {
  FEATURES: ['screen-reader', 'keyboard-navigation', 'high-contrast'],
  COMPLIANCE: ['WCAG2.1', 'ADA'],
};

export const ERROR_MESSAGES = {
  AUTH: {
    INVALID_CREDENTIALS: 'Invalid credentials',
    TOKEN_EXPIRED: 'Token expired',
    UNAUTHORIZED: 'Unauthorized',
  },
  VALIDATION: {
    INVALID_INPUT: 'Invalid input',
    REQUIRED_FIELD: 'Required field',
  },
  SECURITY: {
    ENCRYPTION_FAILED: 'Encryption failed',
    DECRYPTION_FAILED: 'Decryption failed',
  },
  NETWORK: {
    TIMEOUT: 'Request timeout',
    SERVER_ERROR: 'Server error',
  },
};

export const SUCCESS_MESSAGES = {
  AUTH: {
    LOGIN_SUCCESS: 'Login successful',
    REGISTER_SUCCESS: 'Registration successful',
    LOGOUT_SUCCESS: 'Logout successful',
  },
  USER: {
    PROFILE_UPDATED: 'Profile updated',
    SETTINGS_UPDATED: 'Settings updated',
  },
  SECURITY: {
    ENCRYPTION_SUCCESS: 'Encryption successful',
    DECRYPTION_SUCCESS: 'Decryption successful',
  },
};

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  DASHBOARD: '/dashboard',
  ANALYTICS: '/analytics',
  SECURITY: '/security',
  ACCESSIBILITY: '/accessibility',
};

export const STORAGE_KEYS = {
  AUTH: {
    TOKEN: 'auth_token',
    REFRESH_TOKEN: 'auth_refresh_token',
  },
  USER: {
    PROFILE: 'user_profile',
    SETTINGS: 'user_settings',
  },
  APP: {
    THEME: 'app_theme',
    LANGUAGE: 'app_language',
  },
};

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

export const LANGUAGES = {
  EN: 'en',
  ES: 'es',
  FR: 'fr',
  DE: 'de',
  RU: 'ru',
};

export const BREAKPOINTS = {
  XS: 0,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1400,
};

export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
};

// API Endpoints
export const API = {
  EXTENSIONS: 'https://api.novabrowser.org/extensions',
  UPDATES: 'https://api.novabrowser.org/updates',
  SYNC: 'https://api.novabrowser.org/sync',
  FEEDBACK: 'https://api.novabrowser.org/feedback',
};

// Extension Categories
export const EXTENSION_CATEGORIES = {
  PRODUCTIVITY: 'productivity',
  PRIVACY: 'privacy',
  SOCIAL: 'social',
  SHOPPING: 'shopping',
  NEWS: 'news',
  ENTERTAINMENT: 'entertainment',
  TOOLS: 'tools',
  DEVELOPER: 'developer',
  ACCESSIBILITY: 'accessibility',
  EDUCATION: 'education',
} as const;

// Extension Permissions
export const EXTENSION_PERMISSIONS = {
  STORAGE: 'storage',
  TABS: 'tabs',
  BOOKMARKS: 'bookmarks',
  HISTORY: 'history',
  DOWNLOADS: 'downloads',
  NOTIFICATIONS: 'notifications',
  GEOLOCATION: 'geolocation',
  CAMERA: 'camera',
  MICROPHONE: 'microphone',
  CLIPBOARD: 'clipboard',
  NETWORK: 'network',
} as const;

// Browser Features
export const FEATURES = {
  EXTENSIONS: 'extensions',
  SYNC: 'sync',
  PROFILES: 'profiles',
  THEMES: 'themes',
  PRIVACY: 'privacy',
  SECURITY: 'security',
  ACCESSIBILITY: 'accessibility',
  DEVELOPER_TOOLS: 'developerTools',
} as const;

// Theme Types
export const THEME_TYPES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
  CUSTOM: 'custom',
} as const;

// Error Types
export const ERROR_TYPES = {
  NETWORK: 'network',
  PERMISSION: 'permission',
  VALIDATION: 'validation',
  RUNTIME: 'runtime',
  SECURITY: 'security',
} as const;

// Event Types
export const EVENT_TYPES = {
  EXTENSION_INSTALLED: 'extension:installed',
  EXTENSION_UNINSTALLED: 'extension:uninstalled',
  EXTENSION_UPDATED: 'extension:updated',
  EXTENSION_ERROR: 'extension:error',
  TAB_CREATED: 'tab:created',
  TAB_CLOSED: 'tab:closed',
  TAB_UPDATED: 'tab:updated',
  BOOKMARK_ADDED: 'bookmark:added',
  BOOKMARK_REMOVED: 'bookmark:removed',
  HISTORY_CLEARED: 'history:cleared',
  DOWNLOAD_STARTED: 'download:started',
  DOWNLOAD_COMPLETED: 'download:completed',
  DOWNLOAD_FAILED: 'download:failed',
  SYNC_STARTED: 'sync:started',
  SYNC_COMPLETED: 'sync:completed',
  SYNC_FAILED: 'sync:failed',
} as const;

// Cache Keys
export const CACHE_KEYS = {
  EXTENSIONS: 'extensions',
  THEMES: 'themes',
  SETTINGS: 'settings',
  USER_DATA: 'userData',
} as const;

// Timeouts
export const TIMEOUTS = {
  EXTENSION_LOAD: 5000,
  SYNC: 30000,
  UPDATE_CHECK: 3600000,
  TAB_SLEEP: 300000,
  CACHE_CLEANUP: 86400000,
} as const;

// Limits
export const LIMITS = {
  MAX_TABS: 100,
  MAX_EXTENSIONS: 50,
  MAX_BOOKMARKS: 10000,
  MAX_HISTORY: 100000,
  MAX_DOWNLOADS: 1000,
  MAX_PROFILES: 10,
  MAX_THEMES: 100,
} as const;

// Regular Expressions
export const REGEX = {
  URL: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  VERSION: /^\d+\.\d+\.\d+$/,
} as const;

// File Types
export const FILE_TYPES = {
  IMAGE: ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
  AUDIO: ['mp3', 'wav', 'ogg', 'm4a'],
  VIDEO: ['mp4', 'webm', 'mov', 'avi'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz'],
} as const;

// MIME Types
export const MIME_TYPES = {
  HTML: 'text/html',
  CSS: 'text/css',
  JAVASCRIPT: 'application/javascript',
  JSON: 'application/json',
  XML: 'application/xml',
  PDF: 'application/pdf',
  IMAGE: 'image/*',
  AUDIO: 'audio/*',
  VIDEO: 'video/*',
  FONT: 'font/*',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Keyboard Shortcuts
export const SHORTCUTS = {
  NEW_TAB: 'Ctrl+T',
  CLOSE_TAB: 'Ctrl+W',
  NEW_WINDOW: 'Ctrl+N',
  PRIVATE_WINDOW: 'Ctrl+Shift+N',
  BOOKMARK_PAGE: 'Ctrl+D',
  HISTORY: 'Ctrl+H',
  DOWNLOADS: 'Ctrl+J',
  SETTINGS: 'Ctrl+,',
  EXTENSIONS: 'Ctrl+Shift+E',
  DEVELOPER_TOOLS: 'Ctrl+Shift+I',
  RELOAD: 'Ctrl+R',
  FORCE_RELOAD: 'Ctrl+Shift+R',
  ZOOM_IN: 'Ctrl++',
  ZOOM_OUT: 'Ctrl+-',
  RESET_ZOOM: 'Ctrl+0',
} as const;
