import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

import { LANGUAGES } from '../constants';

// Import translations
import deTranslation from './locales/de.json';
import enTranslation from './locales/en.json';
import esTranslation from './locales/es.json';
import frTranslation from './locales/fr.json';
import jaTranslation from './locales/ja.json';
import koTranslation from './locales/ko.json';
import ruTranslation from './locales/ru.json';
import zhTranslation from './locales/zh.json';

const resources = {
  en: {
    translation: enTranslation,
  },
  ru: {
    translation: ruTranslation,
  },
  es: {
    translation: esTranslation,
  },
  fr: {
    translation: frTranslation,
  },
  de: {
    translation: deTranslation,
  },
  zh: {
    translation: zhTranslation,
  },
  ja: {
    translation: jaTranslation,
  },
  ko: {
    translation: koTranslation,
  },
};

const languageConfigs = {
  [LANGUAGES.EN]: {
    name: 'English',
    nativeName: 'English',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.ES]: {
    name: 'Spanish',
    nativeName: 'Español',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.FR]: {
    name: 'French',
    nativeName: 'Français',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.DE]: {
    name: 'German',
    nativeName: 'Deutsch',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.RU]: {
    name: 'Russian',
    nativeName: 'Русский',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.ZH]: {
    name: 'Chinese',
    nativeName: '中文',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.JA]: {
    name: 'Japanese',
    nativeName: '日本語',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
  [LANGUAGES.KO]: {
    name: 'Korean',
    nativeName: '한국어',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
};

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: LANGUAGES.EN,
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    react: {
      useSuspense: true,
    },
  });

export { i18n, languageConfigs };

// Language utilities
export const languages = {
  en: {
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
  },
  ru: {
    name: 'Russian',
    nativeName: 'Русский',
    direction: 'ltr',
  },
  es: { name: 'Spanish', nativeName: 'Español' },
  fr: { name: 'French', nativeName: 'Français' },
  de: { name: 'German', nativeName: 'Deutsch' },
  zh: { name: 'Chinese', nativeName: '中文' },
  ja: { name: 'Japanese', nativeName: '日本語' },
  ko: { name: 'Korean', nativeName: '한국어' },
} as const;

export type LanguageCode = keyof typeof languages;

export const getLanguageName = (code: LanguageCode): string => {
  return languages[code].name;
};

export const getNativeLanguageName = (code: LanguageCode): string => {
  return languages[code].nativeName;
};

export const getLanguageDirection = (code: LanguageCode): 'ltr' | 'rtl' => {
  return languages[code].direction;
};

export const isRTL = (code: LanguageCode): boolean => {
  return languages[code].direction === 'rtl';
};

export const getCurrentLanguage = (): LanguageCode => {
  return (i18n.language as LanguageCode) || 'en';
};

export const setLanguage = async (code: LanguageCode): Promise<void> => {
  await i18n.changeLanguage(code);
  document.documentElement.lang = code;
  document.documentElement.dir = getLanguageDirection(code);
};

export const formatDate = (date: Date, locale: string) => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

export const formatNumber = (number: number, locale: string) => {
  return new Intl.NumberFormat(locale).format(number);
};

export const formatCurrency = (amount: number, locale: string, currency: string) => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatRelativeTime = (date: Date, locale: string) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

  if (days > 0) return rtf.format(-days, 'day');
  if (hours > 0) return rtf.format(-hours, 'hour');
  if (minutes > 0) return rtf.format(-minutes, 'minute');
  return rtf.format(-seconds, 'second');
};
