import i18n from '../config';

describe('i18n configuration', () => {
  it('should default to en language', () => {
    expect(i18n.language).toBe('en');
  });

  it('can switch to ru', async () => {
    await i18n.changeLanguage('ru');
    expect(i18n.language).toBe('ru');
  });

  it('translation key exists for ru and en', () => {
    expect(i18n.t('app.title', { lng: 'ru' })).toBeDefined();
    expect(i18n.t('app.title', { lng: 'en' })).toBeDefined();
  });
});
